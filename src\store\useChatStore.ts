import { create } from 'zustand';
import { Chat, Message, TypingEvent, CreateChatDto, SendMessageDto } from '../types/chat';
import { chatService } from '../services/api/chat.service';
import { websocketService } from '../services/websocket.service';
import { showErrorToast } from '../utils/showErrorToast';

interface ChatState {
  // State
  chats: Chat[];
  currentChat: Chat | null;
  messages: { [chatId: string]: Message[] };
  loading: boolean;
  error: string | null;
  connected: boolean;
  typingUsers: { [chatId: string]: TypingEvent[] };
  totalUnreadCount: number;

  // Pagination
  chatPage: number;
  chatHasMore: boolean;
  messagesCursors: { [chatId: string]: string | undefined };
  messagesHasMore: { [chatId: string]: boolean };

  // Actions
  initializeWebSocket: (token: string) => void;
  disconnectWebSocket: () => void;
  loadChats: (page?: number) => Promise<void>;
  loadMessages: (chatId: string, loadMore?: boolean) => Promise<void>;
  createChat: (createChatDto: CreateChatDto) => Promise<Chat | null>;
  sendMessage: (chatId: string, content: string) => Promise<void>;
  setCurrentChat: (chat: Chat | null) => void;
  markAsRead: (chatId: string) => Promise<void>;
  addMessage: (message: Message) => void;
  updateTyping: (event: TypingEvent) => void;
  clearError: () => void;
  refreshUnreadCount: () => Promise<void>;
}

export const useChatStore = create<ChatState>((set, get) => ({
  // Initial state
  chats: [],
  currentChat: null,
  messages: {},
  loading: false,
  error: null,
  connected: false,
  typingUsers: {},
  totalUnreadCount: 0,
  chatPage: 1,
  chatHasMore: true,
  messagesCursors: {},
  messagesHasMore: {},

  // Initialize WebSocket connection
  initializeWebSocket: (token: string) => {
    websocketService.connect(token);

    // Set up event listeners
    websocketService.onConnection((connected) => {
      set({ connected });
    });

    websocketService.onMessage((message) => {
      // Validate message data before processing
      if (!message || !message.id) {
        console.warn('Received invalid message data:', message);
        return;
      }

      // Ensure chatId is present - if not, try to get it from current chat
      const { currentChat } = get();
      const messageWithChatId: Message = {
        ...message,
        chatId: message.chatId || currentChat?.id
      };

      if (!messageWithChatId.chatId) {
        console.warn('Cannot determine chatId for message:', message);
        return;
      }

      get().addMessage(messageWithChatId);
      // Update chat's last message and unread count
      const { chats } = get();
      const updatedChats = chats.map(chat => {
        if (chat && chat.id === messageWithChatId.chatId) {
          return {
            ...chat,
            lastMessage: messageWithChatId,
            unreadCount: currentChat?.id === messageWithChatId.chatId ? 0 : (chat.unreadCount || 0) + 1
          };
        }
        return chat;
      });
      set({ chats: updatedChats });
      get().refreshUnreadCount();
    });

    websocketService.onTyping((event) => {
      get().updateTyping(event);
    });

    websocketService.onError((error) => {
      console.error('WebSocket error:', error);
      showErrorToast('Connection error occurred');
    });
  },

  // Disconnect WebSocket
  disconnectWebSocket: () => {
    websocketService.disconnect();
    set({ connected: false });
  },

  // Load chats with pagination
  loadChats: async (page = 1) => {
    set({ loading: true, error: null });
    try {
      const response = await chatService.getChats(page, 20);
      const { chats: newChats, totalPages } = response;

      // Filter out any invalid chats and ensure participants array exists
      const validChats = (newChats || []).filter(chat =>
        chat &&
        chat.id &&
        Array.isArray(chat.participants)
      ).map(chat => ({
        ...chat,
        participants: chat.participants || [],
        unreadCount: chat.unreadCount || 0
      }));

      if (page === 1) {
        set({
          chats: validChats,
          chatPage: 1,
          chatHasMore: totalPages > 1
        });
      } else {
        set(state => ({
          chats: [...state.chats, ...validChats],
          chatPage: page,
          chatHasMore: page < totalPages
        }));
      }

      await get().refreshUnreadCount();
    } catch (error: any) {
      console.error('Failed to load chats:', error);
      set({ error: error.message || 'Failed to load chats' });
      showErrorToast('Failed to load chats');
    } finally {
      set({ loading: false });
    }
  },

  // Load messages for a chat
  loadMessages: async (chatId: string, loadMore = false) => {
    set({ loading: true, error: null });
    try {
      const cursor = loadMore ? get().messagesCursors[chatId] : undefined;
      const response = await chatService.getMessages(chatId, cursor, 20);
      const { messages: newMessages, hasMore, nextCursor } = response;

      set(state => {
        const existingMessages = state.messages[chatId] || [];
        // Don't reverse - keep messages in chronological order (oldest first)
        const updatedMessages = loadMore
          ? [...newMessages, ...existingMessages]
          : newMessages;

        return {
          messages: {
            ...state.messages,
            [chatId]: updatedMessages
          },
          messagesCursors: {
            ...state.messagesCursors,
            [chatId]: nextCursor
          },
          messagesHasMore: {
            ...state.messagesHasMore,
            [chatId]: hasMore
          }
        };
      });

      // Join the chat room for real-time updates
      if (websocketService.connected) {
        websocketService.joinChat(chatId);
      }
    } catch (error: any) {
      console.error('Failed to load messages:', error);
      set({ error: error.message || 'Failed to load messages' });
      showErrorToast('Failed to load messages');
    } finally {
      set({ loading: false });
    }
  },

  // Create a new chat
  createChat: async (createChatDto: CreateChatDto) => {
    set({ loading: true, error: null });
    try {
      const newChat = await chatService.createChat(createChatDto);
      set(state => ({
        chats: [newChat, ...state.chats],
        currentChat: newChat
      }));
      return newChat;
    } catch (error: any) {
      console.error('Failed to create chat:', error);
      set({ error: error.message || 'Failed to create chat' });
      showErrorToast('Failed to create chat');
      return null;
    } finally {
      set({ loading: false });
    }
  },

  // Send a message
  sendMessage: async (chatId: string, content: string) => {
    try {
      const message = await chatService.sendMessage(chatId, { content });

      // Immediately add the message to local state for instant feedback
      if (message) {
        // Ensure the message has the chatId
        const messageWithChatId: Message = {
          ...message,
          chatId: chatId
        };
        get().addMessage(messageWithChatId);
      }
    } catch (error: any) {
      console.error('Failed to send message:', error);
      showErrorToast('Failed to send message');
    }
  },

  // Set current chat
  setCurrentChat: (chat: Chat | null) => {
    const { currentChat } = get();

    // Leave previous chat room
    if (currentChat && websocketService.connected) {
      websocketService.leaveChat(currentChat.id);
    }

    set({ currentChat: chat });

    // Join new chat room and mark as read
    if (chat) {
      if (websocketService.connected) {
        websocketService.joinChat(chat.id);
      }
      get().markAsRead(chat.id);
      get().loadMessages(chat.id);
    }
  },

  // Mark chat as read
  markAsRead: async (chatId: string) => {
    try {
      await chatService.markAsRead(chatId);
      // Update local state
      set(state => ({
        chats: state.chats.map(chat =>
          chat.id === chatId ? { ...chat, unreadCount: 0 } : chat
        )
      }));
      get().refreshUnreadCount();
    } catch (error: any) {
      console.error('Failed to mark as read:', error);
    }
  },

  // Add a new message (from WebSocket)
  addMessage: (message: Message) => {
    // Validate message data
    if (!message || !message.id || !message.chatId) {
      console.warn('Attempted to add invalid message:', message);
      return;
    }

    const chatId = message.chatId;
    if (!chatId) {
      console.warn('Message missing chatId:', message);
      return;
    }

    set(state => {
      const existingMessages = state.messages[chatId] || [];
      const messageExists = existingMessages.some((m: Message) => m && m.id === message.id);

      if (messageExists) {
        return state; // Don't add duplicate messages
      }

      // Ensure message has required fields
      const validatedMessage: Message = {
        ...message,
        sender: message.sender || { id: 0, username: 'Unknown' } as any,
        content: message.content || '',
        type: message.type || 'text',
        status: message.status || 'sent',
        isRead: message.isRead || false,
        createdAt: message.createdAt || new Date().toISOString(),
        chatId: chatId
      };

      return {
        messages: {
          ...state.messages,
          [chatId]: [...existingMessages, validatedMessage]
        }
      };
    });
  },

  // Update typing indicators
  updateTyping: (event: TypingEvent) => {
    set(state => {
      const chatTyping = state.typingUsers[event.chatId] || [];

      if (event.isTyping) {
        // Add user to typing list if not already there
        const userExists = chatTyping.some(t => t.userId === event.userId);
        if (!userExists) {
          return {
            typingUsers: {
              ...state.typingUsers,
              [event.chatId]: [...chatTyping, event]
            }
          };
        }
      } else {
        // Remove user from typing list
        return {
          typingUsers: {
            ...state.typingUsers,
            [event.chatId]: chatTyping.filter(t => t.userId !== event.userId)
          }
        };
      }

      return state;
    });
  },

  // Clear error
  clearError: () => set({ error: null }),

  // Refresh total unread count
  refreshUnreadCount: async () => {
    try {
      const count = await chatService.getTotalUnreadCount();
      set({ totalUnreadCount: count });
    } catch (error) {
      console.error('Failed to refresh unread count:', error);
    }
  }
}));
