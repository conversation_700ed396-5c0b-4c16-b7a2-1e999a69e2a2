import React, { useState } from 'react';
import { Chat, ChatType } from '../../types/chat';
import { Users, User, MoreVertical, Info, UserPlus } from 'lucide-react';
import useAuthStore from '../../store/useAuthStore';
import ChatParticipants from './ChatParticipants';

interface ChatHeaderProps {
  chat: Chat;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({ chat }) => {
  const { user } = useAuthStore();
  const [showParticipants, setShowParticipants] = useState(false);
  const [showOptions, setShowOptions] = useState(false);

  const getChatDisplayName = (): string => {
    if (!chat || !chat.participants) {
      return 'Unknown Chat';
    }

    if (chat.type === ChatType.GROUP && chat.name) {
      return chat.name;
    }

    // For direct chats, filter out the current user and show the other participant's name
    if (chat.type === ChatType.DIRECT && user?.id) {
      const otherParticipant = chat.participants.find(p => p && p.id !== user.id);
      return otherParticipant?.username || 'Unknown User';
    }

    // For group chats without a name, show participant names (excluding current user)
    const validParticipants = chat.participants.filter(p => p && p.username && p.id !== user?.id);
    return validParticipants.length > 0
      ? validParticipants.map(p => p.username).join(', ')
      : 'Unknown Chat';
  };

  const getChatSubtitle = (): string => {
    if (!chat || !chat.participants) {
      return 'Unknown';
    }

    if (chat.type === ChatType.GROUP) {
      return `${chat.participants.length} participants`;
    }

    // For direct chats, could show online status in the future
    return 'Direct message';
  };

  return (
    <>
      <div className="p-3 sm:p-4 border-b border-gray-700 flex items-center justify-between">
        <div className="flex items-center min-w-0 flex-1">
          {/* Avatar */}
          <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-600 rounded-full flex items-center justify-center text-white font-semibold mr-2 sm:mr-3 flex-shrink-0">
            {chat.type === ChatType.GROUP ? (
              <Users className="w-4 h-4 sm:w-5 sm:h-5" />
            ) : (
              <User className="w-4 h-4 sm:w-5 sm:h-5" />
            )}
          </div>

          {/* Chat Info */}
          <div className="min-w-0 flex-1">
            <h2 className="font-semibold text-white text-base sm:text-lg truncate">
              {getChatDisplayName()}
            </h2>
            <p className="text-xs sm:text-sm text-gray-400 truncate">
              {getChatSubtitle()}
            </p>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
          {chat.type === 'group' && (
            <button
              onClick={() => setShowParticipants(true)}
              className="p-1.5 sm:p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
              title="View participants"
            >
              <Info className="w-4 h-4 sm:w-5 sm:h-5" />
            </button>
          )}

          <div className="relative">
            <button
              onClick={() => setShowOptions(!showOptions)}
              className="p-1.5 sm:p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            >
              <MoreVertical className="w-4 h-4 sm:w-5 sm:h-5" />
            </button>

            {/* Options Dropdown */}
            {showOptions && (
              <div className="absolute right-0 top-full mt-1 bg-gray-700 border border-gray-600 rounded-lg shadow-lg z-10 min-w-48">
                <div className="py-1">
                  {chat.type === ChatType.GROUP && (
                    <button
                      onClick={() => {
                        setShowParticipants(true);
                        setShowOptions(false);
                      }}
                      className="w-full px-4 py-2 text-left text-white hover:bg-gray-600 flex items-center"
                    >
                      <Users className="w-4 h-4 mr-2" />
                      View Participants
                    </button>
                  )}

                  <button
                    onClick={() => setShowOptions(false)}
                    className="w-full px-4 py-2 text-left text-white hover:bg-gray-600 flex items-center"
                  >
                    <Info className="w-4 h-4 mr-2" />
                    Chat Info
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Participants Modal */}
      {showParticipants && (
        <ChatParticipants
          chat={chat}
          isOpen={showParticipants}
          onClose={() => setShowParticipants(false)}
        />
      )}

      {/* Click outside to close options */}
      {showOptions && (
        <div
          className="fixed inset-0 z-5"
          onClick={() => setShowOptions(false)}
        />
      )}
    </>
  );
};

export default ChatHeader;
