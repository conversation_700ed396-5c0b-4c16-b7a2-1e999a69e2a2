import { useEffect, useRef } from 'react';
import { websocketService } from '../services/websocket.service';
import { useChatStore } from '../store/useChatStore';
import useAuthStore from '../store/useAuthStore';

export const useWebSocket = () => {
  const { access_token } = useAuthStore();
  const { initializeWebSocket, disconnectWebSocket, connected } = useChatStore();
  const initialized = useRef(false);

  useEffect(() => {
    if (access_token && !initialized.current) {
      initializeWebSocket(access_token);
      initialized.current = true;
    }

    return () => {
      if (initialized.current) {
        disconnectWebSocket();
        initialized.current = false;
      }
    };
  }, [access_token, initializeWebSocket, disconnectWebSocket]);

  return {
    connected,
    startTyping: websocketService.startTyping.bind(websocketService),
    stopTyping: websocketService.stopTyping.bind(websocketService),
    joinChat: websocketService.joinChat.bind(websocketService),
    leaveChat: websocketService.leaveChat.bind(websocketService),
    sendMessage: websocketService.sendMessage.bind(websocketService),
    markAsRead: websocketService.markAsRead.bind(websocketService)
  };
};
