import React from 'react';
import { Chat, ChatType } from '../../types/chat';
import { X, User, Crown, MoreVertical } from 'lucide-react';
import { Link } from 'react-router-dom';
import useAuthStore from '../../store/useAuthStore';

interface ChatParticipantsProps {
  chat: Chat;
  isOpen: boolean;
  onClose: () => void;
}

const ChatParticipants: React.FC<ChatParticipantsProps> = ({ chat, isOpen, onClose }) => {
  const { user } = useAuthStore();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg w-full max-w-md mx-4 max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-xl font-semibold text-white">
            Chat Participants ({chat.participants.length})
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white p-1"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Participants List */}
        <div className="flex-1 overflow-y-auto p-4">
          <div className="space-y-3">
            {chat.participants.map((participant) => {
              const isCurrentUser = participant.id === user?.id;

              return (
                <div
                  key={participant.id}
                  className="flex items-center justify-between p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors"
                >
                  <div className="flex items-center">
                    {/* Avatar */}
                    <div className="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center text-white font-semibold mr-3">
                      <User className="w-5 h-5" />
                    </div>

                    {/* User Info */}
                    <div>
                      <div className="flex items-center">
                        <span className="font-medium text-white">
                          {participant.username}
                        </span>
                        {isCurrentUser && (
                          <span className="ml-2 text-xs text-gray-400">(You)</span>
                        )}
                      </div>

                      {/* Could add online status here in the future */}
                      <div className="text-sm text-gray-400">
                        Member
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    {!isCurrentUser && (
                      <>
                        {/* View Profile */}
                        <Link
                          to={`/users/${participant.id}`}
                          className="text-neonBlue hover:text-blue-400 text-sm"
                          onClick={onClose}
                        >
                          View Profile
                        </Link>

                        {/* More Options (Future feature) */}
                        <button
                          className="text-gray-400 hover:text-white p-1"
                          title="More options"
                        >
                          <MoreVertical className="w-4 h-4" />
                        </button>
                      </>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-700">
          <div className="text-sm text-gray-400 text-center">
            {chat.type === ChatType.GROUP ? 'Group Chat' : 'Direct Message'}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatParticipants;
