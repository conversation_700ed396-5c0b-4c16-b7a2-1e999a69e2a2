import { User } from "./user";

// Enums to match backend
export enum ChatType {
  DIRECT = 'direct',
  GROUP = 'group',
}

export enum MessageType {
  TEXT = 'text',
  SYSTEM = 'system',
}

export enum MessageStatus {
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
}

export interface Chat {
  id: string;
  type: ChatType;
  name?: string;
  description?: string;
  participants: User[];
  lastMessage?: LastMessage;
  unreadCount: number;
  lastMessageAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  messages?: Message[];
}

export interface LastMessage {
  id: string;
  content: string;
  type: MessageType;
  status: MessageStatus;
  sender: User;
  isRead: boolean;
  editedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface Message {
  id: string;
  content: string;
  type: MessageType;
  status: MessageStatus;
  sender: User;
  isRead: boolean;
  editedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  readStatuses?: ReadStatus[];
}

export interface ReadStatus {
  id: string;
  user: User;
  readAt: Date | null;
  createdAt: Date;
}

export interface MessageReadStatus {
  id: string;
  user: User;
  readAt: Date | null;
  createdAt: Date;
}

export interface CreateChatDto {
  type: ChatType;
  participantIds: number[];
  name?: string;
  description?: string;
}

export interface SendMessageDto {
  content: string;
  type?: MessageType;
}

export interface ChatListResponse {
  chats: Chat[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface MessageListResponse {
  messages: Message[];
  total: number;
  hasMore: boolean;
  nextCursor?: string;
}

export interface UnreadCountResponse {
  unreadCount: number;
}

export interface TypingEvent {
  chatId: string;
  userId: number;
  username: string;
  isTyping: boolean;
}

export interface MessageEvent {
  type: 'message_sent' | 'message_read' | 'user_joined' | 'user_left';
  chatId: string;
  message?: Message;
  userId?: number;
  username?: string;
}

// Updated WebSocket event types to match backend
export interface WebSocketEvents {
  // Client to server events
  join_chat: { chatId: string };
  leave_chat: { chatId: string };
  send_message: { chatId: string; message: SendMessageDto };
  mark_read: { chatId: string };
  typing_start: { chatId: string };
  typing_stop: { chatId: string };

  // Server to client events
  connected: { message: string; userId: number };
  joined_chat: { chatId: string; message: string };
  left_chat: { chatId: string; message: string };
  new_message: { chatId: string; message: Message };
  messages_read: { chatId: string; userId: number; markedCount: number };
  user_typing: TypingEvent;
  error: { message: string; event?: string };
}

// WebSocket connection data
export interface WebSocketConnectionData {
  token: string; // Plain token without "Bearer" prefix
}

// Chat service response types
export interface ChatResponse {
  success: boolean;
  data?: any;
  error?: string;
}
